#!/usr/bin/env python3
"""
步骤执行节点

负责协调决策Agent和执行Agent的工作流程，统一管理截图时序
"""

from datetime import datetime
from time import sleep
from typing import Dict, Any

from loguru import logger

from src.domain.ui_task.mobile.aggregate.agent_aggregate import agent_aggregate
from src.domain.ui_task.mobile.android.screenshot_manager import take_screenshot
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.repo.do.task_stop_manager import \
    task_stop_manager
from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService, LogRole
from src.domain.ui_task.mobile.service.task_persistence_service import \
    task_persistence_service
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from src.schema.action_types import ExecutionStatus


def _format_verification_log_entry(step_number: int, verification_result: Dict[str, Any]) -> str:
    """
    格式化验证日志条目

    Args:
        step_number: 步骤编号
        verification_result: 验证结果

    Returns:
        格式化的验证日志条目
    """
    timestamp = datetime.now().strftime("%m-%d %H:%M:%S")
    status = verification_result.get("status", "failed")
    reason = verification_result.get("reason", "")

    # 根据状态设置状态描述
    status_desc = {
        ExecutionStatus.SUCCEED.value: "✅ 验证通过",
        ExecutionStatus.PROCESSING.value: "🔄 执行中",
        ExecutionStatus.FAILED.value: "❌ 验证失败",
        ExecutionStatus.TERMINATE.value: "🛑 用户终止",
    }.get(status, "❓ 未知状态")

    log_entry = f"[{timestamp}] 步骤{step_number}验证\n验证: {status_desc}\n结果: {reason}\n"

    return log_entry


def _extract_key_decision_info(decision_content: str) -> str:
    return _escape_template_variables(decision_content)


def _extract_execution_agent_log(coordinate_response: str) -> str:
    """
    提取执行agent的关键日志信息

    Args:
        coordinate_response: 坐标模型的完整响应

    Returns:
        完整的执行日志
    """
    if not coordinate_response:
        return ""

    # 查找Thought部分
    if "Thought:" in coordinate_response:
        parts = coordinate_response.split("Thought:")
        if len(parts) > 1:
            thought_part = parts[1].split("Action:")[0].strip()
            # 返回完整的thought内容，不再截断
            return thought_part

    return ""


def _extract_coordinate_action(coordinate_response: str) -> str:
    """
    从坐标响应中提取带坐标的动作命令

    Args:
        coordinate_response: 坐标模型的完整响应

    Returns:
        带坐标的动作命令
    """
    if not coordinate_response:
        return ""

    # 查找Action部分
    if "Action:" in coordinate_response:
        parts = coordinate_response.split("Action:")
        if len(parts) > 1:
            action_part = parts[1].strip()
            # 提取第一行作为动作命令
            action_lines = action_part.split('\n')
            if action_lines:
                return action_lines[0].strip()

    return ""


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def get_action_fields(fields):
    formatted_parts = []
    if fields.get("action_decision"):
        escaped_value = _escape_template_variables(fields['action_decision'])
        formatted_parts.append(f"{escaped_value}")
    if fields.get("instruction"):
        escaped_value = _escape_template_variables(fields['instruction'])
        formatted_parts.append(f"操作指令: {escaped_value}")
    if fields.get("action"):
        escaped_value = _escape_template_variables(fields['action'])
        formatted_parts.append(f"执行动作: {escaped_value}")
    return "\n".join(formatted_parts)


def format_parsed_fields_to_string(fields):
    """
    将解析的字段格式化为普通字符串，用换行分隔
    只保留指定的4个关键字段

    Args:
        fields: 解析的字段字典

    Returns:
        格式化的字符串
    """
    formatted_parts = []

    # 只保留用户指定的4个字段，并对内容进行转义
    # if fields.get("self_check"):
    #     escaped_value = _escape_template_variables(fields['self_check'])
    #     formatted_parts.append(f"- 自检结果: {escaped_value}")
    if fields.get("interface_analysis"):
        escaped_value = _escape_template_variables(fields['interface_analysis'])
        formatted_parts.append(f"- 界面分析: {escaped_value}")
    if fields.get("current_step_name"):
        escaped_value = _escape_template_variables(fields['current_step_name'])
        formatted_parts.append(f"- 步骤名称: {escaped_value}")
    if fields.get("action_decision"):
        escaped_value = _escape_template_variables(fields['action_decision'])
        formatted_parts.append(f"- 执行决策: {escaped_value}")
    if fields.get("action"):
        escaped_value = _escape_template_variables(fields['action'])
        formatted_parts.append(f"- 执行动作: {escaped_value}")
    # if fields.get("instruction"):
    #     escaped_value = _escape_template_variables(fields['instruction'])
    #     formatted_parts.append(f"- 操作指令: {escaped_value}")

    return "\n".join(formatted_parts)


def execute_current_step_node(state: DeploymentState) -> DeploymentState:
    """
    协调决策Agent、执行Agent和监督Agent的工作流程，统一管理截图时序
    支持分步用例和聚合用例的不同执行方式
    """
    # 检查任务是否已完成
    if _is_task_completed(state):
        return state

    # 检查任务是否被停止
    if _is_task_stopped(state):
        return _handle_task_stopped(state)

    # 执行监督检查
    state = _perform_supervision_check(state)
    if _should_stop_after_supervision(state):
        return state

    # 检查是否为分步用例，如果是则使用简单的分步执行方法
    verification_mode = state.get("verification_mode", "aggregation")
    if verification_mode == "step_by_step":
        return _execute_step_by_step_simple(state)
    else:
        # 聚合用例使用原有的执行流程
        return _execute_aggregation_case(state)


def _execute_step_by_step_simple(state: DeploymentState) -> DeploymentState:
    """
    简单的分步执行方法 - 直接循环执行步骤中的动作命令
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 🔄 Using simple step-by-step execution mode")

    try:
        # 获取步骤信息
        task_steps = state.get("task_steps", [])
        step_expected_results = state.get("step_expected_results", [])

        if not task_steps:
            logger.error(f"[{task_id}] ❌ No task steps found for step-by-step execution")
            state["completed"] = True
            state["execution_status"] = "FAILED"
            state["error_message"] = "No task steps found for step-by-step execution"
            return state

        # 初始化步骤执行状态
        current_step_index = state.get("current_step_index", 0)
        max_step_retries = state.get("max_step_retries", 3)
        step_retry_counts = state.get("step_retry_counts", [0] * len(task_steps))

        # 确保重试计数数组长度正确
        while len(step_retry_counts) < len(task_steps):
            step_retry_counts.append(0)

        logger.info(f"[{task_id}] 📋 Found {len(task_steps)} steps, current step: {current_step_index + 1}")

        # 检查是否所有步骤都已完成
        if current_step_index >= len(task_steps):
            logger.info(f"[{task_id}] 🎉 All {len(task_steps)} steps completed successfully!")
            state["completed"] = True
            state["execution_status"] = "SUCCESS"
            return state

        # 执行当前步骤
        current_step = task_steps[current_step_index]
        logger.info(f"[{task_id}] 🎯 Executing step {current_step_index + 1}/{len(task_steps)}: {current_step}")

        # 从步骤描述中提取动作命令（假设步骤描述就是动作命令）
        # 这里可以根据实际需求调整步骤到动作的转换逻辑
        action_command = _extract_action_from_step(current_step)

        if action_command:
            # 直接执行动作命令
            from src.domain.ui_task.mobile.android.action_tool import execute_simple_action
            action_result = execute_simple_action(action_command, state["device"])

            if action_result.get("status") == "success":
                logger.info(f"[{task_id}] ✅ Step {current_step_index + 1} executed successfully")
                # 进入下一步
                state["current_step_index"] = current_step_index + 1
                step_retry_counts[current_step_index] = 0
            else:
                # 执行失败，增加重试计数
                step_retry_counts[current_step_index] += 1
                logger.warning(f"[{task_id}] ⚠️ Step {current_step_index + 1} failed, retry count: {step_retry_counts[current_step_index]}")

                if step_retry_counts[current_step_index] >= max_step_retries:
                    logger.error(f"[{task_id}] ❌ Step {current_step_index + 1} failed after {max_step_retries} retries")
                    state["completed"] = True
                    state["execution_status"] = "FAILED"
                    state["error_message"] = f"Step {current_step_index + 1} failed after {max_step_retries} attempts"
                    return state
        else:
            logger.warning(f"[{task_id}] ⚠️ No action command extracted from step: {current_step}")
            # 跳过当前步骤
            state["current_step_index"] = current_step_index + 1

        # 更新状态
        state["step_retry_counts"] = step_retry_counts

        # 添加执行记录
        _add_execution_record(state)

    except Exception as e:
        _handle_execution_error(state, e)

    return state


def _extract_action_from_step(step_description: str) -> str:
    """
    从步骤描述中提取动作命令
    这里可以根据实际需求实现步骤到动作的转换逻辑
    """
    # 简单实现：假设步骤描述就包含动作命令
    # 可以根据实际需求添加更复杂的解析逻辑
    step_lower = step_description.lower().strip()

    # 移除步骤序号
    import re
    step_clean = re.sub(r'^\s*\d+[.)、\-\s]+', '', step_description.strip())

    # 根据关键词识别动作类型
    if "点击" in step_clean or "click" in step_lower:
        # 提取点击目标，这里简化处理
        return f"click"  # 实际应用中需要更复杂的解析
    elif "输入" in step_clean or "type" in step_lower:
        return f"type"
    elif "滑动" in step_clean or "swipe" in step_lower:
        return f"swipe"
    elif "等待" in step_clean or "wait" in step_lower:
        return f"wait 2"
    elif "返回" in step_clean or "back" in step_lower:
        return f"back"
    else:
        # 默认等待动作
        return f"wait 1"


def _execute_aggregation_case(state: DeploymentState) -> DeploymentState:
    """
    执行聚合用例的方法（原有逻辑）
    """
    try:
        # 使用统一的截图时序管理
        state = _execute_with_unified_screenshot_flow(state)

        # 添加执行记录
        _add_execution_record(state)

    except Exception as e:
        _handle_execution_error(state, e)

    return state


def _is_task_completed(state: DeploymentState) -> bool:
    """
    检查任务是否已完成
    """
    if state.get("completed", False):
        task_id = state["task_id"]
        logger.info(f"[{task_id}] ✓ Test case already completed")
        return True
    return False


def _is_task_stopped(state: DeploymentState) -> bool:
    """
    检查任务是否被停止
    """
    task_id = state["task_id"]
    return task_id and task_stop_manager.is_task_stopped(task_id)


def _handle_task_stopped(state: DeploymentState) -> DeploymentState:
    """
    处理任务停止
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 🛑 Task {task_id} has been stopped by user")

    state["completed"] = True
    state["execution_status"] = ExecutionStatus.TERMINATE.value
    state["error_message"] = "用户停止"

    # Add stop record
    state["history"].append({
        "action": "task_stopped",
        "task_id": task_id,
        "reason": "user_request",
        "timestamp": datetime.now().isoformat(),
        "status": "stopped"
    })

    return state


def _perform_supervision_check(state: DeploymentState) -> DeploymentState:
    """
    执行监督检查
    """
    app_package = state.get("app_package", "")
    if app_package:
        state = agent_aggregate.supervise_execution(state, app_package)
    return state


def _should_stop_after_supervision(state: DeploymentState) -> bool:
    """
    检查监督后是否应该停止
    """
    if state.get("completed", False) and state.get("execution_status") == "failed":
        task_id = state["task_id"]
        logger.info(f"[{task_id}] ❌ Supervisor stopped execution due to error detection")
        return True
    return False


def _add_execution_record(state: DeploymentState) -> None:
    """
    添加执行记录
    """
    execution_success = True
    if len(state["history"]) > 0:
        last_record = state["history"][-1]
        if last_record.get("status") == "error":
            execution_success = False

    execution_record = {
        "action": "test_case_execution",
        "execution_success": execution_success,
        "timestamp": datetime.now().isoformat(),
        "total_steps": len(state.get("task_steps", []))
    }

    state["history"].append(execution_record)


def _handle_execution_error(state: DeploymentState, error: Exception) -> None:
    """
    处理执行错误
    """
    task_id = state["task_id"]

    # 使用统一的异常处理器
    TaskExceptionHandler.handle_task_exception(
        task_id=task_id,
        error=error,
        context="Test case execution",
        state=state
    )

    # 设置重试相关状态
    state["step_failed"] = True
    state["retry_count"] += 1

    # 记录执行失败日志
    TaskExceptionHandler.log_execution_failure(task_id, str(error))


def _execute_with_unified_screenshot_flow(state: DeploymentState) -> DeploymentState:
    """
    统一的截图时序管理流程：
    1. 执行第一步截图（当前界面状态）
    2. 决策agent分析（使用当前截图）
    3. 执行agent处理（使用同一张截图）
    4. 执行后截图（动作完成后的界面状态）
    5. 存储到state中（更新current_page.screenshot）
    """
    # 初始化执行轮次
    _initialize_execution_count(state)

    # 第1步：拍摄当前界面截图
    before_screenshot_path = _take_and_validate_screenshot(state)
    if not before_screenshot_path:
        return _handle_screenshot_failure(state)

    current_action = None
    try:
        # 检查任务是否在决策前被停止
        if _check_task_stopped_before_decision(state, before_screenshot_path):
            return state

        # 提前创建动作记录（在决策开始前），以正确记录整个流程的耗时
        current_action = _create_preliminary_action_record(state)

        # 第2步：决策agent分析
        parsed_fields, action_line = _perform_decision_analysis(state, before_screenshot_path)

        # 更新动作记录的详细信息
        _update_action_record_details(current_action, parsed_fields, action_line)

        if action_line:
            # 处理不同类型的动作
            return _process_action_execution(state, action_line, parsed_fields, current_action,
                                             before_screenshot_path)
        else:
            return _handle_no_action_extracted(state, current_action, before_screenshot_path)

    except Exception as e:
        return _handle_execution_exception(state, e, before_screenshot_path, current_action)


def _initialize_execution_count(state: DeploymentState) -> None:
    """
    初始化执行轮次
    """
    state["execution_count"] = state.get("execution_count", 0) + 1
    current_execution_count = state["execution_count"]
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 🔄 开始第{current_execution_count}轮执行")

    # 立即记录轮次开始的系统日志
    try:
        from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
        round_start_log = ExecutionLogService.create_system_log(f"第{current_execution_count}轮执行开始")
        task_persistence_service.append_execution_log_entries(task_id, [round_start_log])
    except Exception as e:
        logger.warning(f"[{task_id}] Failed to log round start: {str(e)}")


def _take_and_validate_screenshot(state: DeploymentState) -> str:
    """
    拍摄并验证截图
    """
    sleep(2)
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 📸 Step 1: Taking screenshot of current interface state...")
    return _take_current_screenshot(state)


def _handle_screenshot_failure(state: DeploymentState) -> DeploymentState:
    """
    处理截图失败
    """
    state["history"].append({
        "action": "enhanced_get_location",
        "error": "Failed to take initial screenshot",
        "status": "error"
    })
    return state


def _handle_base64_conversion_failure(state: DeploymentState) -> DeploymentState:
    """
    处理base64转换失败
    """
    state["history"].append({
        "action": "enhanced_get_location",
        "error": "Failed to convert screenshot to base64",
        "status": "error"
    })
    return state


def _check_task_stopped_before_decision(state: DeploymentState, before_screenshot_path: str) -> bool:
    """
    检查任务是否在决策前被停止
    """
    task_id = state["task_id"]
    if task_id and task_stop_manager.is_task_stopped(task_id):
        logger.info(f"[{task_id}] 🛑 Task {task_id} stopped before decision analysis")

        state["completed"] = True
        state["execution_status"] = ExecutionStatus.TERMINATE.value
        state["error_message"] = "用户停止"

        state["history"].append({
            "action": "task_stopped_before_decision",
            "task_id": task_id,
            "before_screenshot": before_screenshot_path,
            "timestamp": datetime.now().isoformat(),
            "status": "stopped"
        })

        return True
    return False


def _perform_decision_analysis(state: DeploymentState, before_screenshot_path: str) -> tuple:
    """
    执行决策分析
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 🧠 Step 2: Decision agent analyzing current interface...")
    parsed_fields, action_line = agent_aggregate.analyze_and_decide(state, before_screenshot_path)

    # 将解析的字段存储到state中以便后续使用
    state["decision_fields"] = parsed_fields

    return parsed_fields, action_line


def _create_preliminary_action_record(state: DeploymentState):
    """
    在决策开始前创建初步的动作记录，以正确记录整个流程的耗时
    """
    task_id = state["task_id"]
    current_action = None

    if task_id:
        # 创建初步的动作记录，步骤名称和动作命令稍后更新
        current_action = task_persistence_service.create_task_action(
            task_id=task_id,
            step_name="决策中...",  # 临时步骤名称
            action_command="",  # 临时动作命令
            decision_content=""  # 临时决策内容
        )

    return current_action


def _update_action_record_details(current_action, parsed_fields: dict, action_line: str):
    """
    更新动作记录的详细信息（在决策完成后）
    """
    if current_action and parsed_fields:
        # 更新动作记录的详细信息
        formatted_decision_content = get_action_fields(parsed_fields)
        step_name = parsed_fields.get("current_step_name", "未知步骤")

        # 使用update_action_extra_info方法更新详细信息
        task_persistence_service.action_repo.update_action_extra_info(
            action_id=current_action.id,
            before_screenshot=None,  # 稍后设置
            verification_result=None,  # 稍后设置
            final_action_command=action_line or ""
        )

        # 更新步骤名称和决策内容（需要直接更新数据库）
        _update_action_step_and_decision(current_action.id, step_name, formatted_decision_content)


def _update_action_step_and_decision(action_id: int, step_name: str, decision_content: str):
    """
    更新动作记录的步骤名称和决策内容
    """
    try:
        from src.domain.ui_task.mobile.repo.ui_task_repository import DBSessionContext
        from src.domain.ui_task.mobile.repo.dao import UITaskAction

        with DBSessionContext() as session:
            action = session.query(UITaskAction).filter(UITaskAction.id == action_id).first()
            if action:
                action.step_name = step_name
                action.decision_content = decision_content
                action.updated_at = datetime.now()
                session.commit()
    except Exception as e:
        logger.warning(f"Failed to update action step and decision: {str(e)}")


def _create_action_record(state: DeploymentState, parsed_fields: dict, action_line: str):
    """
    创建动作记录（保留原有方法以兼容性）
    """
    task_id = state["task_id"]
    current_action = None

    if action_line and task_id:
        formatted_decision_content = get_action_fields(parsed_fields)
        current_action = task_persistence_service.create_task_action(
            task_id=task_id,
            step_name=parsed_fields.get("current_step_name"),
            action_command=action_line,
            decision_content=formatted_decision_content
        )

    return current_action


def _process_action_execution(state: DeploymentState, action_line: str, parsed_fields: dict,
                              current_action, before_screenshot_path: str) -> DeploymentState:
    """
    处理动作执行
    """
    # 检查是否是完成动作
    if action_line.startswith("finished("):
        return _handle_finished_action(state, action_line, parsed_fields, current_action, before_screenshot_path)

    # 检查是否是失败动作
    if action_line.startswith("failed("):
        return _handle_failed_action(state, action_line, parsed_fields, current_action, before_screenshot_path)

    # 检查任务是否在执行前被停止
    if _check_task_stopped_before_execution(state, action_line, current_action, before_screenshot_path, parsed_fields):
        return state

    # 检查是否被阻塞执行
    if _check_execution_blocked(state, parsed_fields, current_action):
        return state

    # 执行动作
    return _execute_action_and_handle_result(state, action_line, parsed_fields, current_action,
                                             before_screenshot_path)


def _handle_finished_action(state: DeploymentState, action_line: str, parsed_fields: dict,
                            current_action, before_screenshot_path: str) -> DeploymentState:
    """
    处理完成动作
    """
    task_id = state["task_id"]

    # 检查是否存在步骤预期结果验证失败
    if parsed_fields.get("execution_blocked") and parsed_fields.get("block_reason"):
        logger.warning(f"[{task_id}] ⚠️ Finished action blocked due to expectation verification failure")
        # 将finished动作转换为被阻塞的执行，不允许任务完成
        return _handle_blocked_finished_action(state, parsed_fields, current_action)

    logger.info(f"[{task_id}] ✅ UI Task completed: {action_line}")

    # finished动作不需要执行，直接标记为成功
    action_result = {"status": "success", "action": "finished", "message": "Task completed successfully"}

    # 标记整个测试用例完成
    state["completed"] = True
    state["execution_status"] = ExecutionStatus.SUCCEED.value
    state["step_failed"] = False

    # 使用标注后的执行前截图作为完成截图
    after_screenshot_path = before_screenshot_path
    state["current_page"]["screenshot"] = after_screenshot_path

    # 添加完成记录
    formatted_decision_content = format_parsed_fields_to_string(parsed_fields)
    state["history"].append({
        "action": "test_case_completed",
        "decision_content": formatted_decision_content,
        "completion_action": action_line,
        "before_screenshot": before_screenshot_path,
        "after_screenshot": after_screenshot_path,
        "total_steps": len(state.get("task_steps", [])),
        "status": "completed",
        "timestamp": datetime.now().isoformat()
    })

    # 完成最后一个动作记录
    if current_action:
        success = action_result is not None
        error_message = action_result.get("message") if action_result and action_result.get(
            "status") != "success" else None

        # 使用原始截图路径
        annotated_before_screenshot = before_screenshot_path

        task_persistence_service.complete_task_action(
            action_id=current_action.id,
            success=success,
            error_message=error_message,
            before_screenshot=annotated_before_screenshot,
            verification_result=None,
            final_action_command=action_line
        )

        # 更新UITask的image_path为最终截图
        if after_screenshot_path:
            task_persistence_service.update_task_image_path(task_id, after_screenshot_path)

    logger.info(f"[{task_id}] 🎉UI Task execution completed successfully!")
    return state


def _handle_blocked_finished_action(state: DeploymentState, parsed_fields: dict, current_action) -> DeploymentState:
    """
    处理被阻塞的完成动作（由于步骤预期结果验证失败）
    """
    task_id = state["task_id"]
    block_reason = parsed_fields.get("block_reason", "Expectation verification failed")

    logger.warning(f"[{task_id}] 🚫 Finished action blocked: {block_reason}")

    # 获取当前执行轮次
    current_execution_count = state.get("execution_count", 0)

    # 回退execution_count，因为这轮执行被阻塞
    if current_execution_count > 0:
        state["execution_count"] = current_execution_count - 1
        logger.info(
            f"[{task_id}] 🔄 Rolling back execution_count from {current_execution_count} to {state['execution_count']}")

    # 回退到上一个步骤重新执行
    current_step_index = state.get("current_step_index", 0)
    if current_step_index > 0:
        state["current_step_index"] = current_step_index - 1
        logger.info(
            f"[{task_id}] 🔄 Rolling back current_step_index from {current_step_index} to {state['current_step_index']}")

    # 清除当前步骤的验证结果，允许重新验证
    step_verification_results = state.get("step_verification_results", [])
    if current_step_index < len(step_verification_results):
        step_verification_results[current_step_index] = {}
        logger.info(
            f"[{task_id}] 🔄 Cleared verification result for step {current_step_index + 1} to allow re-verification")

    # 使用标记确保验证失败信息只显示一次
    verification_shown_key = f"verification_shown_{current_execution_count}"
    if not state.get(verification_shown_key):
        state["verification_failure_reason"] = block_reason
        state[verification_shown_key] = True
    state["step_failed"] = True

    # 添加包含验证失败信息的历史记录
    formatted_decision_content = format_parsed_fields_to_string(parsed_fields)

    # 获取验证失败的步骤信息（使用原来的step_index）
    task_steps = state.get("task_steps", [])
    failed_step_name = task_steps[current_step_index] if current_step_index < len(
        task_steps) else f"步骤{current_step_index + 1}"

    history_entry = {
        "action": "enhanced_get_location",
        "execution_count": current_execution_count,
        "decision_content": formatted_decision_content,
        "decision_fields": parsed_fields,
        "parsed_action": "finished_blocked",
        "needs_coordinates": False,
        "coordinate_response": "",
        "action_result": {"status": "blocked", "reason": block_reason},
        "before_screenshot": "",
        "after_screenshot": "",
        "total_steps": len(state.get("task_steps", [])),
        "status": "blocked",
        "timestamp": datetime.now().isoformat(),
        "step_verification_failure": {
            "step_index": current_step_index,
            "step_name": failed_step_name,
            "failure_reason": block_reason
        }
    }

    state["history"].append(history_entry)

    # 完成动作记录（标记为失败）
    if current_action:
        TaskExceptionHandler.complete_action_as_failed(
            task_id=task_id,
            action_id=current_action.id,
            error_message=f"Finished action blocked: {block_reason}",
            action_command="finished_blocked"
        )

    return state


def _handle_failed_action(state: DeploymentState, action_line: str, parsed_fields: dict,
                          current_action, before_screenshot_path: str) -> DeploymentState:
    """
    处理失败动作
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] ❌ UI Task failed: {action_line}")

    # 标记整个测试用例失败
    state["completed"] = True
    state["execution_status"] = ExecutionStatus.FAILED.value
    state["step_failed"] = True

    # 提取失败原因
    import re
    content_match = re.search(r"failed\s*\(\s*content\s*=\s*['\"]([^'\"]*)['\"]", action_line)
    failure_reason = content_match.group(1) if content_match else "Decision agent determined task should fail"
    state["error_message"] = failure_reason

    # 使用标注后的执行前截图作为失败截图
    after_screenshot_path = before_screenshot_path
    state["current_page"]["screenshot"] = after_screenshot_path

    # 添加失败记录到历史
    state["history"].append({
        "action": "task_failed",
        "task_id": task_id,
        "failure_reason": failure_reason,
        "decision_content": parsed_fields.get("self_check", ""),
        "interface_analysis": parsed_fields.get("interface_analysis", ""),
        "element_description": parsed_fields.get("element_description", ""),
        "instruction": parsed_fields.get("instruction", ""),
        "action_decision": parsed_fields.get("action_decision", ""),
        "before_screenshot": before_screenshot_path,
        "after_screenshot": after_screenshot_path,
        "timestamp": datetime.now().isoformat(),
        "status": "failed"
    })

    # 完成最后一个动作记录
    if current_action:
        success = False
        error_message = f"Task failed: {failure_reason}"

        # 使用原始截图路径
        annotated_before_screenshot = before_screenshot_path

        task_persistence_service.complete_task_action(
            action_id=current_action.id,
            success=success,
            error_message=error_message,
            before_screenshot=annotated_before_screenshot,
            verification_result=None,
            final_action_command=action_line
        )

        # 更新UITask的image_path为最终截图
        if after_screenshot_path:
            task_persistence_service.update_task_image_path(task_id, after_screenshot_path)

    logger.info(f"[{task_id}] 💥 UI Task execution failed: {failure_reason}")
    return state


def _check_task_stopped_before_execution(state: DeploymentState, action_line: str,
                                         current_action, before_screenshot_path: str,
                                         parsed_fields: dict = None) -> bool:
    """
    检查任务是否在执行前被停止
    """
    task_id = state["task_id"]
    if task_id and task_stop_manager.is_task_stopped(task_id):
        logger.info(f"[{task_id}] 🛑 Task {task_id} stopped before action execution")

        state["completed"] = True
        state["execution_status"] = ExecutionStatus.TERMINATE.value
        state["error_message"] = "用户停止"

        state["history"].append({
            "action": "task_stopped_before_execution",
            "task_id": task_id,
            "planned_action": action_line,
            "before_screenshot": before_screenshot_path,
            "timestamp": datetime.now().isoformat(),
            "status": "stopped"
        })

        # 如果已经创建了动作记录，需要完成它（标记为停止）
        if current_action:
            task_persistence_service.complete_task_action(
                action_id=current_action.id,
                success=False,
                error_message="Task stopped by user request",
                before_screenshot=before_screenshot_path,
                verification_result=None,
                final_action_command=action_line,
                terminate=True
            )

        # 添加决策内容到执行日志
        if parsed_fields:
            current_execution_count = state.get("execution_count", 1)
            formatted_decision_content = format_parsed_fields_to_string(parsed_fields)

            # 创建决策Agent日志
            decision_message = f"{formatted_decision_content}\n执行动作: {action_line}\n状态: 🛑 用户终止"
            decision_log = ExecutionLogService.create_log_entry(LogRole.DECISION_AGENT, decision_message)

            # 创建系统日志
            system_log = ExecutionLogService.create_system_log(f"第{current_execution_count}轮执行（用户停止）")

            task_persistence_service.append_execution_log_entries(task_id, [system_log, decision_log])

        return True
    return False


def _check_execution_blocked(state: DeploymentState, parsed_fields: dict, current_action) -> bool:
    """
    检查是否被阻塞执行
    """
    if parsed_fields.get("execution_blocked", False):
        task_id = state["task_id"]
        block_reason = parsed_fields.get("block_reason", "Unknown reason")
        logger.warning(f"[{task_id}] 🚫 Execution blocked due to expectation verification failure: {block_reason}")

        # 回滚execution_count，因为这轮执行被阻塞了
        current_execution_count = state["execution_count"]
        state["execution_count"] = current_execution_count - 1
        logger.info(
            f"[{task_id}] 🔄 Rolling back execution_count from {current_execution_count} to {state['execution_count']}")

        # 回退到上一个步骤重新执行
        current_step_index = state.get("current_step_index", 0)
        if current_step_index > 0:
            state["current_step_index"] = current_step_index - 1
            logger.info(
                f"[{task_id}] 🔄 Rolling back current_step_index from {current_step_index} to {state['current_step_index']}")

        # 清除当前步骤的验证结果，允许重新验证
        step_verification_results = state.get("step_verification_results", [])
        if current_step_index < len(step_verification_results):
            step_verification_results[current_step_index] = {}
            logger.info(
                f"[{task_id}] 🔄 Cleared verification result for step {current_step_index + 1} to allow re-verification")

        # 使用标记确保验证失败信息只显示一次
        verification_shown_key = f"verification_shown_{current_execution_count}"
        if not state.get(verification_shown_key):
            state["verification_failure_reason"] = block_reason
            state[verification_shown_key] = True
        state["step_failed"] = True

        # 添加包含验证失败信息的历史记录
        formatted_decision_content = format_parsed_fields_to_string(parsed_fields)

        # 获取验证失败的步骤信息（使用原来的step_index）
        task_steps = state.get("task_steps", [])
        failed_step_name = task_steps[current_step_index] if current_step_index < len(
            task_steps) else f"步骤{current_step_index + 1}"

        history_entry = {
            "action": "enhanced_get_location",
            "execution_count": current_execution_count,
            "decision_content": formatted_decision_content,
            "decision_fields": parsed_fields,
            "parsed_action": "execution_blocked",
            "needs_coordinates": False,
            "coordinate_response": "",
            "action_result": {"status": "blocked", "reason": block_reason},
            "before_screenshot": "",
            "after_screenshot": "",
            "total_steps": len(state.get("task_steps", [])),
            "status": "blocked",
            "timestamp": datetime.now().isoformat(),
            "step_verification_failure": {
                "step_index": current_step_index,
                "step_name": failed_step_name,
                "failure_reason": block_reason
            }
        }

        state["history"].append(history_entry)

        # 完成动作记录（标记为失败）
        if current_action:
            TaskExceptionHandler.complete_action_as_failed(
                task_id=task_id,
                action_id=current_action.id,
                error_message=f"Execution blocked: {block_reason}",
                action_command="execution_blocked"
            )

        return True
    return False


def _execute_action_and_handle_result(state: DeploymentState, action_line: str, parsed_fields: dict,
                                      current_action, before_screenshot_path: str) -> DeploymentState:
    """
    执行动作并处理结果
    """
    task_id = state["task_id"]

    # 第4步：执行agent处理
    logger.info(f"[{task_id}] ⚡ Step 4: Execution agent processing action...")
    action_result = agent_aggregate.execute_action(action_line, parsed_fields, state, before_screenshot_path)
    logger.info(f"[{task_id}] ✓ Action processed: {action_line}")

    # 处理验证结果
    verification_result = parsed_fields.get("expectation_verification_result")

    # 添加历史记录
    _add_action_history_entry(state, action_line, parsed_fields, action_result, before_screenshot_path,
                              verification_result)

    # 更新执行日志
    _update_execution_log(state, action_line, parsed_fields, action_result, verification_result)

    # 完成动作记录
    _complete_action_record(current_action, action_result, verification_result, action_line, before_screenshot_path,
                            state)

    return state


def _add_action_history_entry(state: DeploymentState, action_line: str, parsed_fields: dict,
                              action_result: dict, before_screenshot_path: str, verification_result: dict) -> None:
    """
    添加动作历史记录
    """
    current_execution_count = state["execution_count"]
    formatted_decision_content = format_parsed_fields_to_string(parsed_fields)

    history_entry = {
        "action": "enhanced_get_location",
        "execution_count": current_execution_count,
        "decision_content": formatted_decision_content,
        "decision_fields": parsed_fields,
        "parsed_action": action_line,
        "needs_coordinates": agent_aggregate.requires_coordinates(action_line),
        "coordinate_response": action_result.get("coordinate_response", ""),
        "action_result": action_result,
        "before_screenshot": before_screenshot_path,
        "after_screenshot": before_screenshot_path,
        "total_steps": len(state.get("task_steps", [])),
        "status": "success" if action_result is not None else "error",
        "timestamp": datetime.now().isoformat()
    }

    # 添加验证结果到历史记录
    if verification_result:
        history_entry["verification_result"] = verification_result
        history_entry["current_step_index"] = state.get("current_step_index", 0)

    # 添加步骤验证失败信息到历史记录
    verification_failure_reason = state.get("verification_failure_reason")
    if verification_failure_reason:
        current_step_index = state.get("current_step_index", 0)
        task_steps = state.get("task_steps", [])
        current_step_name = task_steps[current_step_index] if current_step_index < len(
            task_steps) else f"步骤{current_step_index + 1}"

        history_entry["step_verification_failure"] = {
            "step_index": current_step_index,
            "step_name": current_step_name,
            "failure_reason": verification_failure_reason
        }

        # 验证失败信息已经记录在历史中，无需在state中保持
        # 注意：不在这里清除verification_failure_reason，由get_step_verification_failure_context统一处理

    state["history"].append(history_entry)


def _update_execution_log(state: DeploymentState, action_line: str, parsed_fields: dict, action_result: dict,
                          verification_result: dict = None) -> None:
    """
    更新执行日志 - Agent日志已经在各自Agent中实时记录，系统日志也已在轮次开始时记录
    """
    # Agent日志已经在各自执行时实时记录
    # 系统日志"第N轮执行开始"已经在_initialize_execution_count中记录
    # 这里不需要再记录任何日志，避免重复
    pass


def _complete_action_record(current_action, action_result: dict,
                            verification_result: dict, action_line: str, before_screenshot_path: str,
                            state: dict) -> None:
    """
    完成动作记录
    """
    if current_action:
        # 根据要求：只要动作执行了（不管是否成功），都判定为执行成功，更新状态和时间
        # 只要action_result存在且不是None，就认为动作已执行
        success = action_result is not None
        error_message = action_result.get("message") if action_result and action_result.get(
            "status") != "success" else None

        # 更新动作记录，如果有坐标响应则使用带坐标的完整动作
        final_action_command = action_line
        coordinate_response = action_result.get("coordinate_response", "") if action_result else ""
        if coordinate_response and agent_aggregate.requires_coordinates(action_line):
            # 从坐标响应中提取带坐标的动作命令
            coordinate_action = _extract_coordinate_action(coordinate_response)
            if coordinate_action:
                final_action_command = coordinate_action

        # 使用原始截图路径
        annotated_before_screenshot = before_screenshot_path

        # 传递标注后的执行前截图路径到UITaskAction，执行后截图路径到UITask
        task_persistence_service.complete_task_action(
            action_id=current_action.id,
            success=success,
            error_message=error_message,
            before_screenshot=annotated_before_screenshot,
            verification_result=verification_result,
            final_action_command=final_action_command
        )

        # 更新UITask的image_path为标注后的截图（显示操作位置）
        if annotated_before_screenshot:
            task_persistence_service.update_task_image_path(state["task_id"], annotated_before_screenshot)


def _handle_no_action_extracted(state: DeploymentState, current_action, before_screenshot_path: str) -> DeploymentState:
    """
    处理没有提取到动作的情况
    """
    task_id = state["task_id"]
    current_execution_count = state.get("execution_count", 0)

    state["history"].append({
        "action": "enhanced_get_location",
        "before_screenshot": before_screenshot_path,
        "error": "Failed to extract action",
        "status": "error",
        "timestamp": datetime.now().isoformat()
    })

    # 检查是否应该标记任务失败（连续多次无法提取动作）
    if TaskExceptionHandler.should_fail_task_on_repeated_errors(
            state=state,
            error_type="Failed to extract action",
            max_errors=3
    ):
        TaskExceptionHandler.handle_task_exception(
            task_id=task_id,
            error=Exception("Failed to extract action multiple times"),
            context="Action extraction",
            state=state
        )

    # 如果已经创建了动作记录，需要完成它（标记为失败）
    if current_action:
        TaskExceptionHandler.complete_action_as_failed(
            task_id=task_id,
            action_id=current_action.id,
            error_message="Failed to extract action",
            action_command=""
        )
    return state


def _handle_execution_exception(state: DeploymentState, error: Exception,
                                before_screenshot_path: str, current_action=None) -> DeploymentState:
    """
    处理执行异常
    """
    task_id = state["task_id"]
    logger.error(f"[{task_id}] ❌ Error in enhanced_get_location: {str(error)}")

    state["history"].append({
        "action": "enhanced_get_location",
        "before_screenshot": before_screenshot_path,
        "error": str(error),
        "status": "error",
        "timestamp": datetime.now().isoformat()
    })

    # 使用统一的异常处理器
    TaskExceptionHandler.handle_task_exception(
        task_id=task_id,
        error=error,
        context="Enhanced get location",
        state=state
    )

    # 如果已经创建了动作记录，需要完成它（标记为失败）
    if current_action:
        TaskExceptionHandler.complete_action_as_failed(
            task_id=task_id,
            action_id=current_action.id,
            error_message=f"Enhanced get location error: {str(error)}",
            action_command=""
        )
    return state


def _take_current_screenshot(state: DeploymentState) -> str:
    """
    拍摄当前界面状态截图

    Args:
        state: 当前状态

    Returns:
        截图文件路径，失败返回空字符串
    """
    try:
        screenshot_path = take_screenshot(
            device=state["device"],
            task_id=state["task_id"],
            execution_count=state.get("execution_count", 0)
        )
        if screenshot_path and not screenshot_path.startswith("Screenshot failed"):
            task_id = state["task_id"]
            logger.info(f"[{task_id}] 📸 New screenshot taken: {screenshot_path}")
            # 更新state中的当前截图
            state["current_page"]["screenshot"] = screenshot_path
            return screenshot_path
        else:
            task_id = state["task_id"]
            logger.info(f"[{task_id}] ❌ Failed to take screenshot: {screenshot_path}")
            return ""

    except Exception as e:
        task_id = state["task_id"]
        logger.info(f"[{task_id}] ❌ Error taking current screenshot: {str(e)}")
        return ""
